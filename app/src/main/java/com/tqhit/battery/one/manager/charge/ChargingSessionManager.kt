package com.tqhit.battery.one.manager.charge

import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ChargingSessionManager @Inject constructor(private val preferencesHelper: PreferencesHelper) {
    companion object {
        private const val KEY_CHARGING_SESSIONS = "charging_sessions"
        private const val MAX_SESSIONS = 60 * 60 * 24 // Maximum number of sessions to keep
    }

    private val sessions = mutableListOf<ChargeSession>()

    init {
        loadSessions()
    }

    fun getAllSessions(): List<ChargeSession> {
        return sessions
    }

    private fun loadSessions() {
        val sessionsJson = preferencesHelper.getString(KEY_CHARGING_SESSIONS)
        android.util.Log.d("ChargingSessionManager", "Loading sessions from prefs: '$sessionsJson'")
        if (!sessionsJson.isNullOrEmpty()) {
            sessions.clear()
            val loadedSessions = sessionsJson.split("|").mapNotNull { ChargeSession.fromString(it) }
            sessions.addAll(loadedSessions)
            android.util.Log.d("ChargingSessionManager", "Loaded ${sessions.size} sessions from preferences")
        } else {
            android.util.Log.d("ChargingSessionManager", "No sessions found in preferences, starting with empty list")
        }
    }

    private fun saveSessions() {
        val sessionsJson = sessions.joinToString("|") { it.toString() }
        preferencesHelper.saveString(KEY_CHARGING_SESSIONS, sessionsJson)
    }

    fun addSession(session: ChargeSession) {
        sessions.add(session)
        if (sessions.size > MAX_SESSIONS) {
            sessions.removeAt(0)
        }
        saveSessions()
    }

    fun clearSessions() {
        sessions.clear()
        saveSessions()
    }

    fun getAverageScreenOnSpeed(): Double {
        val validSessions = sessions.filter { it.screenOnPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnPercent }.average()
        } else 0.0
    }

    fun getAverageScreenOffSpeed(): Double {
        val validSessions = sessions.filter { it.screenOffPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffPercent }.average()
        } else 0.0
    }

    fun getAverageSpeed(): Double {
        val validSessions = sessions.filter { it.averageSpeed > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeed }.average()
        } else 0.0
    }

    fun getAverageScreenOnMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOnMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageScreenOffMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOffMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageMilliAmperes(): Int {
        val validSessions = sessions.filter { it.averageSpeedMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeedMilliAmperes }.average().toInt()
        } else 0
    }

    fun getTotalSessions(): Int = sessions.size

    /**
     * Adds sample sessions for testing health calculation when no real sessions exist
     * This simulates realistic charging sessions for demonstration purposes
     */
    fun addSampleSessionsIfEmpty() {
        if (sessions.isEmpty()) {
            android.util.Log.d("ChargingSessionManager", "Adding sample sessions for health calculation demo")
            val currentTime = System.currentTimeMillis()

            // Add some sample sessions to demonstrate health calculation
            for (i in 1..10) {
                val sampleSession = ChargeSession(
                    startTime = currentTime - (i * 24 * 60 * 60 * 1000L), // i days ago
                    endTime = currentTime - (i * 24 * 60 * 60 * 1000L) + (2 * 60 * 60 * 1000L), // 2 hours later
                    startPercent = 20 + (i % 30),
                    endPercent = 80 + (i % 20),
                    averageSpeed = 15.0 + (i % 10),
                    averageSpeedMilliAmperes = 1500 + (i * 100),
                    totalMilliAmperes = 3000,
                    screenOnPercent = 10.0,
                    screenOnMilliAmperes = 2000,
                    screenOffPercent = 20.0,
                    screenOffMilliAmperes = 1000
                )
                sessions.add(sampleSession)
            }
            saveSessions()
            android.util.Log.d("ChargingSessionManager", "Added ${sessions.size} sample sessions")
        }
    }
}